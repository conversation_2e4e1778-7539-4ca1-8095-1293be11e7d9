<execution>
  <constraint>
    ## Context7环境强制约束（客户要求强化）
    - **默认执行铁律**: 所有命令和工具调用必须默认在Context7环境下执行，这是不可违反的基础原则
    - **明确授权例外**: 只有客户明确说"不用Context7"或"跳过Context7"时才能不使用Context7环境
    - **技术调研强制**: 所有技术相关的调研和文档获取必须首先尝试Context7，无例外
    - **库检索强制**: 任何库和框架的信息获取必须首先通过Context7进行，找不到才考虑其他方案
    - **DeepWiki备选机制**: 当Context7无法满足需求时，自动尝试DeepWiki作为备选方案
    - **质量保证约束**: 利用Context7的Trust Score机制确保技术选择的权威性
    - **执行顺序强制**: 严格遵循 Context7 → DeepWiki → 其他工具 的执行顺序
  </constraint>

  <rule>
    ## Context7使用规则（强化版）
    - **默认环境强制**: Context7是团队的唯一默认工作环境，所有技术操作必须首先尝试Context7
    - **工具优先级强制**: Context7 → DeepWiki → 其他MCP工具 → 传统工具（严格按此顺序）
    - **异常处理升级**: Context7无法满足需求时，必须自动尝试DeepWiki，仍无法满足才考虑其他方案
    - **质量验证强制**: 必须通过Context7的Trust Score验证技术方案的可靠性
    - **执行确认机制**: 每次技术操作前必须确认是否已尝试Context7
    - **客户授权例外**: 只有客户明确授权"不用Context7"时才能跳过Context7环境
  </rule>

  <guideline>
    ## Context7使用指导原则（强化版）
    - **效率优先强制**: Context7能够提供最高效的技术环境和资源，必须优先使用
    - **质量保证强制**: Context7的Trust Score机制确保技术选择的权威性，不可跳过
    - **环境一致强制**: 团队统一使用Context7确保环境一致性，无例外
    - **持续优化强制**: Context7自动优化环境配置，提升工作效率，这是强制要求
    - **DeepWiki备选强制**: Context7无法满足时必须自动尝试DeepWiki，不得直接使用其他工具
    - **执行顺序检查**: 每次操作前必须检查是否遵循了Context7 → DeepWiki → 其他工具的顺序
  </guideline>

  <process>
    ## Context7强制执行流程（客户要求）

    ### 1. 强制环境检查
    ```
    任务开始 → 强制确认Context7环境状态 → 如有问题则必须优先解决环境问题 → 不得跳过此步骤
    ```

    ### 2. Context7优先尝试（强制第一步）
    ```
    技术需求 → 必须首先尝试Context7库检索 → Trust Score评估 → 获取权威文档 → 如成功则采用方案
    ```

    ### 3. DeepWiki备选机制（强制第二步）
    ```
    Context7无法满足 → 自动尝试DeepWiki → deepwiki_fetch_mcp-deepwiki → 如成功则采用方案
    ```

    ### 4. 其他工具最后选择（需要授权）
    ```
    Context7和DeepWiki都无法满足 → 向客户说明原因 → 获得明确授权 → 才能使用其他工具
    ```

    ### 5. 客户授权例外处理
    ```
    客户明确说"不用Context7" → 记录例外原因 → 优先尝试DeepWiki → 再考虑其他工具
    ```

    ### 6. 执行前强制检查清单
    ```
    ✓ 是否已尝试Context7？
    ✓ Context7无法满足时是否已尝试DeepWiki？
    ✓ 是否获得客户明确授权使用其他工具？
    ✓ 是否记录了例外情况和原因？
    ```
  </process>

  <criteria>
    ## Context7使用质量标准（强化版）
    - ✅ 所有技术操作必须默认在Context7环境下进行（强制要求）
    - ✅ 技术调研必须首先通过Context7获取权威信息（无例外）
    - ✅ Trust Score机制必须得到有效利用（质量保证）
    - ✅ Context7无法满足时必须自动尝试DeepWiki（备选机制）
    - ✅ 严格遵循Context7 → DeepWiki → 其他工具的执行顺序（顺序强制）
    - ✅ 客户明确授权例外时必须记录原因（例外管理）
    - ✅ 每次技术操作前必须完成执行前检查清单（流程保证）
    - ✅ 环境配置自动优化，工作效率最大化（性能要求）
  </criteria>
</execution>
