<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1753254849011_gtzxwg9yd" time="2025/07/23 15:14">
    <content>
      庐山派K230图像处理实现经验总结：
    
      实现要点：
      1. 平台差异：庐山派K230使用CanMV而非OpenMV，API略有不同
      2. 模块导入：必须显式导入image模块，否则image.Image()会报错
      3. 二值化实现：work_img.binary([(gray_min, gray_max)])，参数为元组列表
      4. 图像缩放：display_img.draw_image(src_img, 0, 0, x_scale=2.5, y_scale=2.0)
    
      关键代码片段：
      ```python
      # 创建显示画布
      display_img = image.Image(800, 480, image.RGB565)
    
      # 二值化处理
      work_img.binary([(self.gray_min, self.gray_max)])
    
      # 缩放显示
      display_img.draw_image(work_img, 0, 0, x_scale=2.5, y_scale=2.0)
      ```
    
      调试经验：
      - 触屏坐标需要按显示比例映射
      - UI元素在二值化图像上需要使用高对比度颜色
      - 错误处理要考虑image模块创建失败的情况
    
      性能优化：
      - 避免频繁创建大尺寸图像对象
      - 使用copy()方法避免原图像被修改
      - 适当的垃圾回收避免内存泄漏
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753369398628_h57dw7ors" time="2025/07/24 23:03">
    <content>
      CanMV K230 MicroPython环境兼容性问题完整解决方案：
      1) traceback模块不存在 - 使用sys.print_exception替代traceback.print_exception
      2) typing模块不存在 - 移除所有类型注解，如Optional、Dict、List等
      3) sys.version_info是tuple不是namedtuple - 使用索引访问sys.version_info[0][1][2]替代.major.minor.micro属性访问
      4) os.path模块不存在 - 创建兼容函数path_join/path_exists/makedirs_compat替代os.path.join/exists/makedirs
      5) 文件系统操作差异 - 使用os.stat()检查存在性，os.mkdir()创建目录，字符串拼接路径
    </content>
    <tags>#其他</tags>
  </item>
</memory>