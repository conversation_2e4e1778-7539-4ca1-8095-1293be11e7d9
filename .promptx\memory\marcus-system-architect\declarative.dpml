<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1753254813786_zpkhtp7yk" time="2025/07/23 15:13">
    <content>
      庐山派K230图像处理架构设计最佳实践：
    
      技术架构：
      1. 硬件层：K230芯片 + ST7701 LCD(800x480) + 触屏
      2. 框架层：CanMV框架（非OpenMV）
      3. 应用层：Python图像处理应用
    
      关键设计决策：
      - 传感器分辨率：保持320x240避免内存问题
      - 显示策略：创建800x480画布，使用draw_image缩放显示
      - 性能优化：避免直接resize传感器输出，使用显示时缩放
    
      技术要点：
      - 必须导入image模块：import image
      - 二值化API：image.binary([(min_val, max_val)])
      - 缩放显示：draw_image(img, x, y, x_scale=2.5, y_scale=2.0)
      - 触屏坐标映射：需要按显示比例调整UI区域
    
      架构优势：
      - 内存友好：传感器低分辨率
      - 显示流畅：硬件加速缩放
      - 交互响应：触屏坐标精确映射
      - 扩展性好：易于添加新的图像处理算法
    </content>
    <tags>#最佳实践</tags>
  </item>
</memory>