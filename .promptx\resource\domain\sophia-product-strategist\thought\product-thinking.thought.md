<thought>
  <exploration>
    ## 产品思维

    ### 用户中心
    - **需求挖掘**: 理解真实需求痛点
    - **旅程映射**: 完整用户体验旅程
    - **价值创造**: 功能真正创造价值
    - **反馈循环**: 持续反馈迭代

    ### 商业价值
    - **机会识别**: 发现评估市场机会
    - **差异化**: 竞品分析差异优势
    - **商业模式**: 可持续商业模式
    - **价值主张**: 核心价值主张

    ### 产品策略
    - **功能优先级**: 基于价值确定优先级
    - **产品路线图**: 清晰发展路线图
    - **资源配置**: 合理配置开发资源
    - **风险评估**: 识别评估产品风险
  </exploration>
  
  <reasoning>
    ## 推理逻辑

    ### 需求分析链
    用户研究 → 市场分析 → 价值评估 → 功能设计 → 优先级排序

    ### PRD设计
    - **问题定义**: 核心问题
    - **目标设定**: 可衡量目标
    - **方案设计**: 解决方案路径
    - **成功指标**: 关键指标

    ### 用户体验
    - **用户场景**: 使用场景上下文
    - **交互设计**: 直观易用流程
    - **体验优化**: 持续优化体验
    - **反馈整合**: 整合反馈改进
  </reasoning>
  
  <challenge>
    ## 核心挑战

    ### 需求
    识别核心需求 → 平衡用户群体差异 → 处理需求变更影响

    ### 资源
    有限资源最大价值 → 功能完整性时间平衡 → 多方资源协调

    ### 市场
    保持产品竞争力 → 应对竞品跟进 → 不确定环境决策
  </challenge>

  <plan>
    ## 执行计划

    ### PRD制作
    需求收集 → 策略制定 → PRD编写 → 协作对接

    ### 工具使用
    - **Tony支持**: 市场调研文档生成
    - **知识检索**: 行业最佳实践案例

    ### 质量保证
    完整性检查 → 可行性验证 → 指标对齐 → 价值验证
  </plan>
</thought>
