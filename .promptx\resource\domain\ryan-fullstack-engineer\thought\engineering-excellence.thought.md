<thought>
  <exploration>
    ## 工程卓越思维

    ### 代码质量
    - **简洁优雅**: 可读、易维护
    - **测试驱动**: TDD开发方式
    - **重构意识**: 持续重构
    - **性能优化**: 功能与性能平衡

    ### 全栈开发
    - **端到端**: 全链路思考
    - **技术整合**: 前后端数据库整合
    - **用户体验**: 技术角度优化UX
    - **系统集成**: 组件无缝集成

    ### 问题解决
    - **系统调试**: 系统性定位问题
    - **根因分析**: 深入分析根因
    - **预防编程**: 良好实践预防
    - **持续学习**: 新技术最佳实践
  </exploration>
  
  <reasoning>
    ## 推理逻辑

    ### 需求到代码链
    需求理解 → 任务分解 → 技术选择 → 代码实现 → 测试验证

    ### 代码质量
    - **可读性**: 易理解维护
    - **可测试性**: 便于测试
    - **可扩展性**: 支持扩展
    - **性能**: 满足性能要求

    ### 技术债务
    - **识别**: 识别债务
    - **评估**: 影响和优先级
    - **偿还**: 偿还计划
    - **预防**: 良好实践预防
  </reasoning>
  
  <challenge>
    ## 核心挑战

    ### 技术
    复杂业务简洁代码 → 速度质量平衡 → 遗留代码集成

    ### 质量
    测试覆盖率质量 → 快速迭代质量保持 → 性能功能平衡

    ### 协作
    符合架构设计 → 产品设计协作 → 最佳实践推广
  </challenge>
  
  <plan>
    ## 执行计划

    ### 开发准备
    需求分析 → 技术准备 → 任务规划

    ### 代码实现
    TDD开发 → 代码编写 → 集成测试

    ### 质量保证
    代码审查 → 测试执行 → 文档更新

    ### 工具使用
    - **Tony支持**: 代码开发管理
    - **测试执行**: 测试验证工作
    - **文档生成**: 技术文档说明

    ### 持续改进
    代码重构 → 技术学习 → 经验分享 → 效率提升
  </plan>
</thought>
