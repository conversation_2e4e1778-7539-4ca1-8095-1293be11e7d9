<execution>
  <constraint>
    ## 架构层次修改约束
    - **底层禁止修改**: 绝对禁止修改操作系统、数据库引擎、Web服务器等底层组件
    - **框架层禁止修改**: 严禁修改框架核心代码、第三方库源码、系统级配置
    - **应用层专注原则**: 只能在应用层进行开发和修改，包括业务逻辑、用户界面、API接口
    - **边界清晰约束**: 必须明确区分底层、框架层、应用层的边界，不得跨层修改
  </constraint>

  <rule>
    ## 低耦合设计强制规则
    - **依赖倒置**: 高层模块不依赖低层模块，都依赖于抽象接口
    - **接口隔离**: 使用小而专一的接口，避免臃肿的大接口
    - **单一职责**: 每个类和模块只负责一个明确的职责
    - **开闭原则**: 对扩展开放，对修改关闭，通过扩展实现新功能
    - **组合优于继承**: 优先使用组合而非继承来实现代码复用
    - **配置外部化**: 将配置信息外部化，避免硬编码
  </rule>

  <guideline>
    ## 可扩展性设计指导原则
    - **模块化设计**: 将功能拆分为独立的、可复用的模块
    - **插件化架构**: 设计插件接口，支持功能的动态扩展
    - **事件驱动**: 使用事件机制实现模块间的松耦合通信
    - **策略模式**: 将算法和业务逻辑封装为可替换的策略
    - **工厂模式**: 使用工厂模式创建对象，便于扩展新类型
    - **观察者模式**: 实现发布-订阅机制，支持功能的动态扩展
  </guideline>

  <process>
    ## Ryan架构约束工作流程
    
    ### 1. 修改需求分析与层次判断
    ```
    接收修改需求 → 分析修改范围 → 判断修改层次 → 确认是否符合约束
    ```
    
    #### 🔍 层次判断标准
    ```
    底层组件（禁止修改）：
    - 操作系统内核、驱动程序
    - 数据库引擎（MySQL、PostgreSQL等）
    - Web服务器（Nginx、Apache等）
    - 运行时环境（JVM、Node.js运行时等）
    
    框架层（禁止修改）：
    - 框架核心代码（Spring、React、Vue等）
    - 第三方库源码（lodash、axios等）
    - 系统级配置文件（web.xml、application.yml等）
    - 中间件核心组件（Redis、RabbitMQ等）
    
    应用层（允许修改）：
    - 业务逻辑代码
    - 用户界面组件
    - API接口实现
    - 应用配置文件
    - 自定义工具类和帮助函数
    ```
    
    ### 2. 低耦合设计检查
    ```
    Ryan请求Tony: 分析现有代码结构 - 使用 find_symbol + search_for_pattern
    ```
    
    #### 🔗 耦合度评估标准
    ```
    高耦合（需要重构）：
    - 类之间直接实例化依赖
    - 硬编码的配置和常量
    - 大量的if-else分支判断
    - 紧密的继承关系
    
    低耦合（设计良好）：
    - 依赖注入和接口编程
    - 配置文件外部化
    - 策略模式和工厂模式
    - 事件驱动的通信机制
    ```
    
    ### 3. 可扩展性设计实施
    
    #### 📦 模块化设计
    ```
    Ryan请求Tony: 创建模块接口定义 - 使用 create_text_file
    文件路径: /src/interfaces/[ModuleName]Interface.js
    
    Ryan请求Tony: 实现具体模块 - 使用 create_text_file
    文件路径: /src/modules/[ModuleName]/index.js
    ```
    
    **模块设计要求**：
    - 每个模块有清晰的接口定义
    - 模块间通过接口通信，不直接依赖实现
    - 模块内部高内聚，模块间低耦合
    - 支持模块的独立测试和部署
    
    #### 🔌 插件化架构
    ```
    Ryan请求Tony: 设计插件接口 - 使用 create_text_file
    文件路径: /src/core/PluginInterface.js
    
    Ryan请求Tony: 实现插件管理器 - 使用 create_text_file
    文件路径: /src/core/PluginManager.js
    ```
    
    **插件设计要求**：
    - 定义标准的插件接口
    - 实现插件的动态加载和卸载
    - 支持插件的配置和管理
    - 提供插件间的通信机制
    
    #### 📡 事件驱动设计
    ```
    Ryan请求Tony: 创建事件总线 - 使用 create_text_file
    文件路径: /src/core/EventBus.js
    
    Ryan请求Tony: 定义事件类型 - 使用 create_text_file
    文件路径: /src/events/EventTypes.js
    ```
    
    **事件设计要求**：
    - 使用发布-订阅模式
    - 事件类型标准化定义
    - 支持异步事件处理
    - 提供事件的过滤和路由机制
    
    ### 4. 二次开发友好性设计
    
    #### 📚 API设计规范
    ```
    Ryan请求Tony: 设计RESTful API - 使用 create_text_file
    文件路径: /src/api/[ResourceName]Controller.js
    
    Ryan请求Tony: 生成API文档 - 使用 create_text_file
    文件路径: /docs/api/[ResourceName].md
    ```
    
    **API设计要求**：
    - 遵循RESTful设计原则
    - 统一的错误处理和响应格式
    - 完整的API文档和示例
    - 版本控制和向后兼容
    
    #### 🛠️ 开发工具和脚手架
    ```
    Ryan请求Tony: 创建代码生成器 - 使用 create_text_file
    文件路径: /tools/generators/[TemplateName].js
    
    Ryan请求Tony: 编写开发指南 - 使用 create_text_file
    文件路径: /docs/development/ExtensionGuide.md
    ```
    
    **工具设计要求**：
    - 提供代码模板和生成器
    - 编写详细的开发指南
    - 提供调试和测试工具
    - 建立代码规范和最佳实践
    
    ### 5. 维护性增强设计
    
    #### 📝 代码文档化
    ```
    Ryan请求Tony: 添加代码注释 - 使用 replace_symbol_body
    Ryan请求Tony: 生成技术文档 - 使用 create_text_file
    文件路径: /docs/technical/[ComponentName].md
    ```
    
    **文档要求**：
    - 关键函数和类的详细注释
    - 业务逻辑的说明文档
    - 架构设计的技术文档
    - 故障排查和维护指南
    
    #### 🧪 测试覆盖增强
    ```
    Ryan请求Tony: 编写单元测试 - 使用 create_text_file
    文件路径: /tests/unit/[ComponentName].test.js
    
    Ryan请求Tony: 编写集成测试 - 使用 create_text_file
    文件路径: /tests/integration/[FeatureName].test.js
    ```
    
    **测试要求**：
    - 核心业务逻辑100%测试覆盖
    - 接口和集成点的测试
    - 性能和压力测试
    - 自动化测试和持续集成
    
    ### 6. 配置管理和环境适配
    
    #### ⚙️ 配置外部化
    ```
    Ryan请求Tony: 创建配置文件 - 使用 create_text_file
    文件路径: /config/[environment].json
    
    Ryan请求Tony: 实现配置管理器 - 使用 create_text_file
    文件路径: /src/core/ConfigManager.js
    ```
    
    **配置管理要求**：
    - 环境相关配置外部化
    - 支持配置的热更新
    - 配置验证和默认值
    - 敏感信息的安全处理
  </process>

  <criteria>
    ## Ryan架构约束质量标准
    
    ### 层次边界遵循
    - ✅ 严格遵循应用层修改约束，无跨层修改
    - ✅ 底层和框架层完全不被触及
    - ✅ 修改范围清晰明确，边界分明
    - ✅ 架构完整性得到保持
    
    ### 低耦合设计质量
    - ✅ 模块间依赖关系清晰，耦合度低
    - ✅ 接口设计合理，职责单一
    - ✅ 配置外部化，无硬编码
    - ✅ 依赖注入正确实施
    
    ### 可扩展性实现
    - ✅ 支持功能的插件化扩展
    - ✅ 事件驱动机制完善
    - ✅ 模块化设计清晰
    - ✅ 新功能添加无需修改现有代码
    
    ### 二次开发友好性
    - ✅ API设计规范，文档完整
    - ✅ 开发工具和脚手架齐全
    - ✅ 代码结构清晰，易于理解
    - ✅ 扩展指南详细，示例丰富
    
    ### 维护性保障
    - ✅ 代码注释详细，文档完整
    - ✅ 测试覆盖充分，质量可靠
    - ✅ 配置管理规范，环境适配良好
    - ✅ 故障排查和维护便利
  </criteria>
</execution>
