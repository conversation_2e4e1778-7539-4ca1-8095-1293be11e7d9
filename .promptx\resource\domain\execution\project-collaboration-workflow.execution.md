<execution>
  <constraint>
    ## 项目协作流程强制约束
    - **阶段顺序强制**: 必须严格按照 需求分析→架构设计→开发实现→质量验证→项目交付 的顺序执行
    - **角色交接强制**: 每个阶段完成后必须明确交接给下一阶段负责人，不得跳过
    - **Process Supervisor监督强制**: 每个阶段转换都必须经过Process Supervisor确认
    - **记忆传递强制**: 每个角色完成工作后必须记录关键成果供下一角色回忆
    - **客户授权强制**: 重要阶段转换必须获得Lucas的明确授权
  </constraint>

  <rule>
    ## 项目协作执行规则
    - **阶段完成标准**: 每个阶段必须有明确的交付物和验收标准
    - **角色专注原则**: 每个角色只能在自己负责的阶段主导工作
    - **强制交接原则**: 阶段负责人完成工作后必须主动交接给下一阶段
    - **监督介入原则**: Process Supervisor有权在任何阶段介入进行流程纠正
    - **记忆同步原则**: 关键信息必须通过记忆系统在角色间传递
  </rule>

  <guideline>
    ## 协作流程指导原则
    - **清晰交接**: 每次角色切换都要明确说明"我的工作已完成，现在交接给[下一角色]"
    - **状态同步**: 使用统一的项目状态标识，让所有角色了解当前进展
    - **质量把关**: 每个阶段都要有质量检查点
    - **风险预警**: 及时识别和上报可能影响后续阶段的风险
  </guideline>

  <process>
    ## 标准项目协作流程

    ### 🚀 阶段1: 项目启动与需求分析
    ```mermaid
    flowchart LR
        A[Lucas接收需求] --> B[Sequential Thinking分析]
        B --> C[Sophia制作PRD]
        C --> D[David数据需求分析]
        D --> E[Process Supervisor阶段验收]
        E --> F[交接给Marcus]
    ```
    
    **执行标准**:
    - Lucas: 必须使用Sequential Thinking分析客户需求
    - Sophia: 必须制作完整PRD文档
    - David: 必须分析业务数据需求
    - Process Supervisor: 必须验收PRD质量
    - 交接条件: PRD通过Process Supervisor验收

    ### 🏗️ 阶段2: 系统架构设计
    ```mermaid
    flowchart LR
        A[Marcus接收PRD] --> B[回忆项目需求]
        B --> C[Context7技术调研]
        C --> D[系统架构设计]
        D --> E[David数据架构验证]
        E --> F[Process Supervisor架构审查]
        F --> G[交接给Ryan]
    ```
    
    **执行标准**:
    - Marcus: 必须基于Context7进行技术调研和架构设计
    - David: 必须验证数据架构的合理性
    - Process Supervisor: 必须审查架构设计质量
    - 交接条件: 架构设计通过Process Supervisor审查

    ### 💻 阶段3: 开发实现
    ```mermaid
    flowchart LR
        A[Ryan接收架构] --> B[回忆设计方案]
        B --> C[Context7环境准备]
        C --> D[TDD开发实现]
        D --> E[Tony工具支持]
        E --> F[David功能验证]
        F --> G[Process Supervisor代码审查]
        G --> H[交接给团队验收]
    ```
    
    **执行标准**:
    - Ryan: 必须在Context7环境下进行TDD开发
    - Tony: 必须提供所有工具支持
    - David: 必须验证功能的数据效果
    - Process Supervisor: 必须进行代码质量审查
    - 交接条件: 代码通过Process Supervisor质量审查

    ### ✅ 阶段4: 质量验证与交付
    ```mermaid
    flowchart LR
        A[团队联合验收] --> B[David数据验证]
        B --> C[Sophia产品验收]
        C --> D[Marcus架构验证]
        D --> E[Process Supervisor最终审查]
        E --> F[Lucas客户交付]
    ```
    
    **执行标准**:
    - 所有角色: 必须参与联合验收
    - Process Supervisor: 必须进行最终质量审查
    - Lucas: 必须向客户正式交付
    - 交付条件: 通过Process Supervisor最终审查

    ## 🔄 角色交接协议

    ### 标准交接格式
    ```
    🔄 **[当前角色] 工作完成交接**
    ---
    ✅ **已完成工作**: [具体完成内容]
    📋 **交付物**: [具体交付物清单]
    🧠 **关键信息**: [需要下一角色知道的重要信息]
    ⚠️ **注意事项**: [需要特别注意的问题]
    👉 **交接给**: [下一阶段负责角色]
    ---
    ```

    ### 记忆传递机制
    - **项目背景**: Lucas记录，所有角色共享
    - **需求规格**: Sophia记录，Marcus和Ryan重点关注
    - **技术架构**: Marcus记录，Ryan和David重点关注
    - **实现细节**: Ryan记录，所有角色了解
    - **数据洞察**: David记录，所有角色参考
    - **质量标准**: Process Supervisor记录，所有角色遵循

  </process>

  <criteria>
    ## 协作质量评价标准
    
    ### 流程执行质量
    - ✅ 严格按照阶段顺序执行，无跳跃
    - ✅ 每个阶段都有明确的交付物
    - ✅ 角色交接清晰，信息传递完整
    - ✅ Process Supervisor有效监督每个环节
    
    ### 协作效率质量
    - ✅ 角色间无重复工作
    - ✅ 信息传递及时准确
    - ✅ 问题发现和解决及时
    - ✅ 整体项目进度可控
    
    ### 最终交付质量
    - ✅ 满足客户需求规格
    - ✅ 技术架构合理稳定
    - ✅ 代码质量符合标准
    - ✅ 通过全面质量验收
  </criteria>
</execution>
