<execution>
  <constraint>
    ## 团队协作强制约束
    - **角色专注约束**: 每个角色只能在自己负责的阶段主导工作
    - **交接确认约束**: 必须明确说明"我的工作已完成，现在交接给[下一角色]"
    - **记忆传递约束**: 关键信息必须通过promptx remember记录并传递
    - **Process Supervisor监督约束**: 重要阶段转换必须经过Process Supervisor确认
  </constraint>

  <rule>
    ## 协作执行规则
    - **严格按阶段执行**: 需求分析→架构设计→开发实现→质量验证→项目交付
    - **明确交接机制**: 每个阶段完成后必须主动交接给下一阶段负责人
    - **记忆同步机制**: 使用promptx remember/recall在角色间传递关键信息
    - **监督介入机制**: Process Supervisor有权在任何阶段介入纠正流程
  </rule>

  <guideline>
    ## 协作指导原则
    - **清晰沟通**: 每次交接都要说明完成的工作和需要注意的事项
    - **及时记录**: 重要决策和成果及时记录到记忆系统
    - **主动协调**: 遇到问题主动寻求团队协助
    - **质量优先**: 确保每个阶段的交付质量
  </guideline>

  <process>
    ## 快速协作流程

    ### 🚀 阶段1: 项目启动 (Lucas主导)
    **标准流程**:
    1. Lucas接收客户需求
    2. 使用Sequential Thinking分析
    3. 交接给Sophia制作PRD
    4. David参与数据需求分析
    5. Process Supervisor验收PRD
    
    **交接格式**:
    ```
    🔄 **Lucas工作完成交接**
    ✅ 已完成: 需求分析和Sequential Thinking分析
    📋 交付物: 需求分析报告
    🧠 关键信息: [客户核心需求和约束条件]
    👉 交接给: Sophia制作PRD
    ```

    ### 🏗️ 阶段2: 架构设计 (Marcus主导)
    **标准流程**:
    1. Marcus接收PRD
    2. 回忆项目需求 (promptx recall)
    3. Context7技术调研
    4. 系统架构设计
    5. David验证数据架构
    6. Process Supervisor架构审查
    
    **交接格式**:
    ```
    🔄 **Marcus工作完成交接**
    ✅ 已完成: 系统架构设计和技术选型
    📋 交付物: 架构设计文档
    🧠 关键信息: [技术栈选择和架构决策]
    👉 交接给: Ryan进行开发实现
    ```

    ### 💻 阶段3: 开发实现 (Ryan主导)
    **标准流程**:
    1. Ryan接收架构设计
    2. 回忆设计方案 (promptx recall)
    3. Context7环境准备
    4. TDD开发实现
    5. Tony提供工具支持
    6. David功能验证
    7. Process Supervisor代码审查
    
    **交接格式**:
    ```
    🔄 **Ryan工作完成交接**
    ✅ 已完成: 功能开发和测试
    📋 交付物: 完整代码和测试用例
    🧠 关键信息: [实现细节和技术问题]
    👉 交接给: 团队进行联合验收
    ```

    ### ✅ 阶段4: 质量验证 (全团队参与)
    **标准流程**:
    1. 全团队联合验收
    2. David数据效果验证
    3. Sophia产品功能验收
    4. Marcus架构合规验证
    5. Process Supervisor最终审查
    6. Lucas客户交付
    
    **交接格式**:
    ```
    🔄 **团队验收完成交接**
    ✅ 已完成: 全面质量验证
    📋 交付物: 最终产品和文档
    🧠 关键信息: [验收结果和交付说明]
    👉 交接给: Lucas进行客户交付
    ```

    ## 🧠 记忆管理协议

    ### 关键信息记录标准
    - **Lucas记录**: 客户需求、项目目标、重要决策
    - **Sophia记录**: 产品规格、用户需求、功能定义
    - **Marcus记录**: 技术架构、设计决策、技术约束
    - **Ryan记录**: 实现细节、代码结构、技术问题
    - **David记录**: 数据需求、分析结果、效果验证
    - **Tony记录**: 工具配置、环境设置、技术支持
    - **Process Supervisor记录**: 质量标准、流程问题、改进建议

    ### 记忆传递机制
    ```
    当前角色完成工作 → promptx remember [关键信息] → 
    下一角色激活 → promptx recall → 基于记忆继续工作
    ```

    ## ⚠️ 常见问题处理

    ### 流程违规处理
    - **发现违规**: Process Supervisor立即指出
    - **停止执行**: 要求重新按流程执行
    - **记录问题**: 记录违规行为和纠正措施
    - **流程改进**: 基于问题完善流程

    ### 角色冲突处理
    - **明确职责**: 每个角色专注自己的专业领域
    - **协调机制**: 通过Lucas进行统一协调
    - **监督介入**: Process Supervisor有权介入调解
    - **团队决策**: 重大分歧通过团队讨论解决
  </process>

  <criteria>
    ## 协作质量标准
    
    ### 流程执行质量
    - ✅ 严格按阶段顺序执行
    - ✅ 每个阶段都有明确交付物
    - ✅ 角色交接清晰完整
    - ✅ Process Supervisor有效监督
    
    ### 信息传递质量
    - ✅ 关键信息及时记录
    - ✅ 记忆传递准确完整
    - ✅ 角色间沟通顺畅
    - ✅ 问题反馈及时有效
    
    ### 最终交付质量
    - ✅ 满足客户需求
    - ✅ 技术实现稳定
    - ✅ 代码质量优良
    - ✅ 文档完整清晰
  </criteria>
</execution>
